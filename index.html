<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Directory</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1 class="logo">
                <i class="fas fa-users"></i>
                Employee Directory
            </h1>
            
            <!-- Search Bar -->
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Search by name or email" class="search-input">
                <button id="filterBtn" class="filter-btn">
                    <i class="fas fa-filter"></i>
                    Filter
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Controls Section -->
            <div class="controls-section">
                <div class="sort-show-controls">
                    <div class="control-group">
                        <label for="sortSelect">Sort:</label>
                        <select id="sortSelect" class="control-select">
                            <option value="firstName">First Name</option>
                            <option value="lastName">Last Name</option>
                            <option value="department">Department</option>
                            <option value="role">Role</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="showSelect">Show:</label>
                        <select id="showSelect" class="control-select">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>
                
                <button id="addEmployeeBtn" class="add-employee-btn">
                    <i class="fas fa-plus"></i>
                    Add Employee
                </button>
            </div>

            <!-- Employee Grid -->
            <div id="employeeGrid" class="employee-grid">
                <!-- Employee cards will be dynamically generated here -->
            </div>

            <!-- Pagination -->
            <div id="pagination" class="pagination">
                <!-- Pagination controls will be generated here -->
            </div>
        </div>
    </main>

    <!-- Filter Sidebar -->
    <div id="filterSidebar" class="filter-sidebar">
        <div class="filter-content">
            <div class="filter-header">
                <h3>Filter Employees</h3>
                <button id="closeSidebar" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="filter-section">
                <h4>Department</h4>
                <div id="departmentFilters" class="filter-options">
                    <!-- Department checkboxes will be generated here -->
                </div>
            </div>
            
            <div class="filter-section">
                <h4>Role</h4>
                <div id="roleFilters" class="filter-options">
                    <!-- Role checkboxes will be generated here -->
                </div>
            </div>
            
            <div class="filter-actions">
                <button id="clearFilters" class="clear-btn">Clear All</button>
                <button id="applyFilters" class="apply-btn">Apply Filters</button>
            </div>
        </div>
    </div>

    <!-- Add/Edit Employee Modal -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Add Employee</h2>
                <button id="closeModal" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="employeeForm" class="employee-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">First Name *</label>
                        <input type="text" id="firstName" name="firstName" required>
                        <span class="error-message" id="firstNameError"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="lastName">Last Name *</label>
                        <input type="text" id="lastName" name="lastName" required>
                        <span class="error-message" id="lastNameError"></span>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required>
                        <span class="error-message" id="emailError"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="department">Department *</label>
                        <select id="department" name="department" required>
                            <option value="">Select Department</option>
                            <option value="HR">HR</option>
                            <option value="IT">IT</option>
                            <option value="Finance">Finance</option>
                            <option value="Marketing">Marketing</option>
                            <option value="Sales">Sales</option>
                            <option value="Operations">Operations</option>
                        </select>
                        <span class="error-message" id="departmentError"></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="role">Role *</label>
                    <select id="role" name="role" required>
                        <option value="">Select Role</option>
                        <option value="Manager">Manager</option>
                        <option value="Developer">Developer</option>
                        <option value="Analyst">Analyst</option>
                        <option value="Designer">Designer</option>
                        <option value="Coordinator">Coordinator</option>
                        <option value="Specialist">Specialist</option>
                        <option value="Associate">Associate</option>
                        <option value="Director">Director</option>
                    </select>
                    <span class="error-message" id="roleError"></span>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancelBtn" class="cancel-btn">Cancel</button>
                    <button type="submit" id="submitBtn" class="submit-btn">Add Employee</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content delete-modal">
            <div class="modal-header">
                <h2>Confirm Delete</h2>
                <button id="closeDeleteModal" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <p>Are you sure you want to delete this employee?</p>
                <p id="deleteEmployeeName" class="employee-name"></p>
            </div>
            
            <div class="form-actions">
                <button id="cancelDeleteBtn" class="cancel-btn">Cancel</button>
                <button id="confirmDeleteBtn" class="delete-btn">Delete</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Employee Directory App. All rights reserved.</p>
        </div>
    </footer>

    <!-- Overlay -->
    <div id="overlay" class="overlay"></div>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/validation.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
