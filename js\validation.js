/**
 * Form Validation Module
 * Handles all client-side validation for employee forms
 */

/**
 * Validation rules for employee form fields
 */
const validationRules = {
    firstName: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[a-zA-Z\s'-]+$/,
        message: {
            required: 'First name is required',
            minLength: 'First name must be at least 2 characters long',
            maxLength: 'First name cannot exceed 50 characters',
            pattern: 'First name can only contain letters, spaces, hyphens, and apostrophes'
        }
    },
    lastName: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[a-zA-Z\s'-]+$/,
        message: {
            required: 'Last name is required',
            minLength: 'Last name must be at least 2 characters long',
            maxLength: 'Last name cannot exceed 50 characters',
            pattern: 'Last name can only contain letters, spaces, hyphens, and apostrophes'
        }
    },
    email: {
        required: true,
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        maxLength: 100,
        message: {
            required: '<PERSON><PERSON> is required',
            pattern: 'Please enter a valid email address',
            maxLength: '<PERSON><PERSON> cannot exceed 100 characters',
            duplicate: 'This email address is already in use'
        }
    },
    department: {
        required: true,
        message: {
            required: 'Please select a department'
        }
    },
    role: {
        required: true,
        message: {
            required: 'Please select a role'
        }
    }
};

/**
 * Validate a single field
 * @param {string} fieldName - Name of the field to validate
 * @param {string} value - Value to validate
 * @param {number} excludeId - Employee ID to exclude from duplicate check (for editing)
 * @returns {Object} Validation result with isValid and message
 */
function validateField(fieldName, value, excludeId = null) {
    const rules = validationRules[fieldName];
    if (!rules) {
        return { isValid: true, message: '' };
    }

    const trimmedValue = value.trim();

    // Required field validation
    if (rules.required && !trimmedValue) {
        return { isValid: false, message: rules.message.required };
    }

    // Skip other validations if field is empty and not required
    if (!trimmedValue && !rules.required) {
        return { isValid: true, message: '' };
    }

    // Minimum length validation
    if (rules.minLength && trimmedValue.length < rules.minLength) {
        return { isValid: false, message: rules.message.minLength };
    }

    // Maximum length validation
    if (rules.maxLength && trimmedValue.length > rules.maxLength) {
        return { isValid: false, message: rules.message.maxLength };
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(trimmedValue)) {
        return { isValid: false, message: rules.message.pattern };
    }

    // Email duplicate validation
    if (fieldName === 'email') {
        const isDuplicate = employees.some(emp => 
            emp.email.toLowerCase() === trimmedValue.toLowerCase() && 
            emp.id !== excludeId
        );
        if (isDuplicate) {
            return { isValid: false, message: rules.message.duplicate };
        }
    }

    return { isValid: true, message: '' };
}

/**
 * Validate entire form
 * @param {Object} formData - Form data object
 * @param {number} excludeId - Employee ID to exclude from duplicate check (for editing)
 * @returns {Object} Validation result with isValid, errors object, and first error field
 */
function validateForm(formData, excludeId = null) {
    const errors = {};
    let isValid = true;
    let firstErrorField = null;

    // Validate each field
    Object.keys(validationRules).forEach(fieldName => {
        const value = formData[fieldName] || '';
        const validation = validateField(fieldName, value, excludeId);
        
        if (!validation.isValid) {
            errors[fieldName] = validation.message;
            isValid = false;
            if (!firstErrorField) {
                firstErrorField = fieldName;
            }
        }
    });

    return { isValid, errors, firstErrorField };
}

/**
 * Display validation error for a specific field
 * @param {string} fieldName - Name of the field
 * @param {string} message - Error message to display
 */
function showFieldError(fieldName, message) {
    const field = document.getElementById(fieldName);
    const errorElement = document.getElementById(`${fieldName}Error`);
    
    if (field) {
        field.classList.add('error');
        field.setAttribute('aria-invalid', 'true');
        field.setAttribute('aria-describedby', `${fieldName}Error`);
    }
    
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
        errorElement.setAttribute('role', 'alert');
    }
}

/**
 * Clear validation error for a specific field
 * @param {string} fieldName - Name of the field
 */
function clearFieldError(fieldName) {
    const field = document.getElementById(fieldName);
    const errorElement = document.getElementById(`${fieldName}Error`);
    
    if (field) {
        field.classList.remove('error');
        field.removeAttribute('aria-invalid');
        field.removeAttribute('aria-describedby');
    }
    
    if (errorElement) {
        errorElement.textContent = '';
        errorElement.classList.remove('show');
        errorElement.removeAttribute('role');
    }
}

/**
 * Display all form validation errors
 * @param {Object} errors - Errors object from validation
 */
function showFormErrors(errors) {
    // Clear all existing errors first
    clearAllFieldErrors();
    
    // Show new errors
    Object.keys(errors).forEach(fieldName => {
        showFieldError(fieldName, errors[fieldName]);
    });
}

/**
 * Clear all form validation errors
 */
function clearAllFieldErrors() {
    Object.keys(validationRules).forEach(fieldName => {
        clearFieldError(fieldName);
    });
}

/**
 * Real-time validation for a field (on blur/input events)
 * @param {HTMLElement} field - The form field element
 * @param {number} excludeId - Employee ID to exclude from duplicate check
 */
function validateFieldRealTime(field, excludeId = null) {
    const fieldName = field.name;
    const value = field.value;
    
    // Clear previous error
    clearFieldError(fieldName);
    
    // Validate field
    const validation = validateField(fieldName, value, excludeId);
    
    if (!validation.isValid) {
        showFieldError(fieldName, validation.message);
    }
}

/**
 * Sanitize input value
 * @param {string} value - Input value to sanitize
 * @param {string} type - Type of sanitization ('text', 'email', 'select')
 * @returns {string} Sanitized value
 */
function sanitizeInput(value, type = 'text') {
    if (typeof value !== 'string') return '';
    
    // Basic HTML entity encoding
    value = value.replace(/&/g, '&amp;')
                 .replace(/</g, '&lt;')
                 .replace(/>/g, '&gt;')
                 .replace(/"/g, '&quot;')
                 .replace(/'/g, '&#x27;');
    
    switch (type) {
        case 'text':
            // Remove extra whitespace and trim
            return value.replace(/\s+/g, ' ').trim();
        
        case 'email':
            // Convert to lowercase and trim
            return value.toLowerCase().trim();
        
        case 'select':
            // Just trim for select values
            return value.trim();
        
        default:
            return value.trim();
    }
}

/**
 * Get sanitized form data
 * @param {HTMLFormElement} form - The form element
 * @returns {Object} Sanitized form data
 */
function getSanitizedFormData(form) {
    const formData = new FormData(form);
    const sanitizedData = {};
    
    sanitizedData.firstName = sanitizeInput(formData.get('firstName'), 'text');
    sanitizedData.lastName = sanitizeInput(formData.get('lastName'), 'text');
    sanitizedData.email = sanitizeInput(formData.get('email'), 'email');
    sanitizedData.department = sanitizeInput(formData.get('department'), 'select');
    sanitizedData.role = sanitizeInput(formData.get('role'), 'select');
    
    return sanitizedData;
}

/**
 * Setup real-time validation for form fields
 * @param {HTMLFormElement} form - The form element
 * @param {number} excludeId - Employee ID to exclude from duplicate check
 */
function setupRealTimeValidation(form, excludeId = null) {
    const fields = form.querySelectorAll('input, select');
    
    fields.forEach(field => {
        // Validate on blur (when user leaves the field)
        field.addEventListener('blur', () => {
            validateFieldRealTime(field, excludeId);
        });
        
        // Clear errors on input (when user starts typing)
        field.addEventListener('input', () => {
            if (field.classList.contains('error')) {
                clearFieldError(field.name);
            }
        });
        
        // Prevent form submission on Enter key in input fields
        if (field.tagName === 'INPUT') {
            field.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    // Move to next field or submit if it's the last field
                    const formElements = Array.from(form.elements);
                    const currentIndex = formElements.indexOf(field);
                    const nextElement = formElements[currentIndex + 1];
                    
                    if (nextElement && nextElement.type !== 'submit') {
                        nextElement.focus();
                    } else {
                        form.querySelector('button[type="submit"]')?.click();
                    }
                }
            });
        }
    });
}

/**
 * Show success message
 * @param {string} message - Success message to display
 * @param {number} duration - Duration to show message (in milliseconds)
 */
function showSuccessMessage(message, duration = 3000) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.message');
    existingMessages.forEach(msg => msg.remove());
    
    // Create success message element
    const messageElement = document.createElement('div');
    messageElement.className = 'message success show';
    messageElement.innerHTML = `
        <i class="fas fa-check-circle"></i>
        ${message}
    `;
    
    // Insert at the top of main content
    const mainContent = document.querySelector('.main-content .container');
    if (mainContent) {
        mainContent.insertBefore(messageElement, mainContent.firstChild);
        
        // Auto-remove after duration
        setTimeout(() => {
            messageElement.classList.remove('show');
            setTimeout(() => messageElement.remove(), 300);
        }, duration);
    }
}

/**
 * Show error message
 * @param {string} message - Error message to display
 * @param {number} duration - Duration to show message (in milliseconds)
 */
function showErrorMessage(message, duration = 5000) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.message');
    existingMessages.forEach(msg => msg.remove());
    
    // Create error message element
    const messageElement = document.createElement('div');
    messageElement.className = 'message error show';
    messageElement.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        ${message}
    `;
    
    // Insert at the top of main content
    const mainContent = document.querySelector('.main-content .container');
    if (mainContent) {
        mainContent.insertBefore(messageElement, mainContent.firstChild);
        
        // Auto-remove after duration
        setTimeout(() => {
            messageElement.classList.remove('show');
            setTimeout(() => messageElement.remove(), 300);
        }, duration);
    }
}
