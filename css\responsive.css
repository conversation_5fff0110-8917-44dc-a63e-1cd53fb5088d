/* Responsive Design */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }
    
    .employee-grid {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    .employee-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-container {
        width: 100%;
        justify-content: center;
    }
    
    .search-input {
        width: 100%;
        max-width: 400px;
    }
    
    .controls-section {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .sort-show-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .add-employee-btn {
        align-self: center;
    }
    
    .employee-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
    }
    
    .filter-sidebar {
        width: 350px;
        right: -350px;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .pagination-info {
        width: 100%;
        text-align: center;
        margin: 0.5rem 0;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    .container {
        padding: 0 15px;
    }
    
    .header {
        padding: 0.75rem 0;
    }
    
    .logo {
        font-size: 1.5rem;
    }
    
    .search-container {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .search-input {
        width: 100%;
    }
    
    .filter-btn {
        width: 100%;
        justify-content: center;
    }
    
    .main-content {
        padding: 1.5rem 0;
    }
    
    .controls-section {
        gap: 1rem;
    }
    
    .sort-show-controls {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }
    
    .control-group {
        justify-content: space-between;
        width: 100%;
    }
    
    .control-select {
        min-width: 120px;
    }
    
    .employee-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .employee-card {
        padding: 1rem;
    }
    
    .employee-header {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .employee-avatar {
        align-self: center;
    }
    
    .employee-actions {
        justify-content: center;
        gap: 0.75rem;
    }
    
    .edit-btn, .delete-btn {
        flex: 1;
        justify-content: center;
    }
    
    .filter-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .filter-content {
        padding: 1.5rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .employee-form {
        padding: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .cancel-btn, .submit-btn, .delete-btn {
        width: 100%;
    }
    
    .pagination {
        justify-content: center;
    }
    
    .pagination-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 10px;
    }
    
    .header {
        padding: 0.5rem 0;
    }
    
    .logo {
        font-size: 1.3rem;
    }
    
    .main-content {
        padding: 1rem 0;
    }
    
    .employee-card {
        padding: 0.75rem;
        border-radius: 8px;
    }
    
    .employee-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .employee-name {
        font-size: 1.1rem;
    }
    
    .employee-detail {
        font-size: 0.9rem;
    }
    
    .employee-detail strong {
        min-width: 70px;
        font-size: 0.85rem;
    }
    
    .edit-btn, .delete-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }
    
    .filter-content {
        padding: 1rem;
    }
    
    .filter-header h3 {
        font-size: 1.2rem;
    }
    
    .modal-content {
        width: 98%;
        margin: 0.5rem;
        max-height: 95vh;
    }
    
    .modal-header {
        padding: 0.75rem;
    }
    
    .modal-header h2 {
        font-size: 1.3rem;
    }
    
    .modal-body {
        padding: 0.75rem;
    }
    
    .employee-form {
        padding: 0.75rem;
    }
    
    .form-group label {
        font-size: 0.9rem;
    }
    
    .form-group input,
    .form-group select {
        padding: 0.6rem;
        font-size: 0.9rem;
    }
    
    .pagination-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }
    
    .pagination-info {
        font-size: 0.9rem;
    }
}

/* Extra Small Mobile (up to 400px) */
@media (max-width: 400px) {
    .container {
        padding: 0 8px;
    }
    
    .logo {
        font-size: 1.2rem;
    }
    
    .search-input {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
    }
    
    .filter-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .employee-card {
        padding: 0.6rem;
    }
    
    .employee-name {
        font-size: 1rem;
    }
    
    .employee-detail {
        font-size: 0.85rem;
    }
    
    .employee-detail strong {
        min-width: 60px;
        font-size: 0.8rem;
    }
    
    .edit-btn, .delete-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    .add-employee-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .control-select {
        font-size: 0.9rem;
        padding: 0.4rem;
    }
    
    .modal-header h2 {
        font-size: 1.2rem;
    }
    
    .form-group input,
    .form-group select {
        padding: 0.5rem;
        font-size: 0.85rem;
    }
    
    .cancel-btn, .submit-btn, .delete-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .controls-section,
    .filter-sidebar,
    .modal,
    .overlay,
    .pagination,
    .footer {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
    }
    
    .employee-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .employee-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .employee-actions {
        display: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .employee-card {
        border: 2px solid #000;
    }
    
    .edit-btn {
        background: #000;
        color: #fff;
    }
    
    .delete-btn {
        background: #000;
        color: #fff;
    }
    
    .add-employee-btn {
        background: #000;
        color: #fff;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
