# Employee Directory Web Application

A responsive and interactive Employee Directory built with HTML, CSS, JavaScript, and Freemarker templates. This application provides a comprehensive interface for managing employee information with advanced features like search, filtering, sorting, and pagination.

## 🚀 Features

### Core Functionality
- **Employee Management**: Add, edit, and delete employee records
- **Search**: Real-time search by name or email with debounced input
- **Filtering**: Filter employees by department and role
- **Sorting**: Sort employees by first name, last name, department, or role
- **Pagination**: Configurable pagination (10, 25, 50, 100 items per page)

### User Experience
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Interactive UI**: Smooth animations and transitions
- **Keyboard Navigation**: Full keyboard accessibility support
- **Real-time Validation**: Client-side form validation with instant feedback
- **Error Handling**: Comprehensive error handling and user feedback

### Technical Features
- **Vanilla JavaScript**: No external JavaScript libraries required
- **Modular Code**: Clean, well-documented, and maintainable code structure
- **Freemarker Integration**: Server-side template rendering support
- **Local Storage**: Client-side data persistence (simulated backend)
- **Accessibility**: ARIA labels and keyboard navigation support

## 📁 Project Structure

```
employee-directory/
├── index.html                 # Main HTML file
├── templates/
│   └── employee-directory.ftl # Freemarker template
├── css/
│   ├── styles.css            # Main stylesheet
│   └── responsive.css        # Responsive design styles
├── js/
│   ├── data.js              # Data management and CRUD operations
│   ├── validation.js        # Form validation logic
│   └── app.js               # Main application logic
└── README.md                # Project documentation
```

## 🛠️ Installation & Setup

1. **Clone or download** the project files to your local directory
2. **Open** `index.html` in a modern web browser
3. **For Freemarker integration**: Use `templates/employee-directory.ftl` with your server-side application

### Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No additional dependencies or build tools required

## 💻 Usage

### Basic Operations

#### Adding an Employee
1. Click the "Add Employee" button
2. Fill in the required fields (First Name, Last Name, Email, Department, Role)
3. Click "Add Employee" to save

#### Editing an Employee
1. Click the "Edit" button on any employee card
2. Modify the desired fields
3. Click "Update Employee" to save changes

#### Deleting an Employee
1. Click the "Delete" button on any employee card
2. Confirm the deletion in the popup modal

### Search and Filtering

#### Search
- Use the search bar in the header to search by name or email
- Search is performed in real-time with a 300ms debounce

#### Filtering
1. Click the "Filter" button in the header
2. Select desired departments and/or roles
3. Click "Apply Filters"
4. Use "Clear All" to remove all filters

#### Sorting
- Use the "Sort" dropdown to sort by:
  - First Name
  - Last Name
  - Department
  - Role

#### Pagination
- Use the "Show" dropdown to change items per page
- Navigate using Previous/Next buttons or page numbers

### Keyboard Shortcuts
- **Ctrl/Cmd + F**: Focus search bar
- **Ctrl/Cmd + N**: Open add employee modal
- **Escape**: Close any open modal or sidebar

## 🎨 Responsive Design

The application is fully responsive and adapts to different screen sizes:

- **Desktop (1200px+)**: Full layout with sidebar filters
- **Tablet (768px-1199px)**: Adapted layout with collapsible elements
- **Mobile (576px-767px)**: Single-column layout with touch-friendly controls
- **Small Mobile (≤575px)**: Optimized for small screens

## 🔧 Freemarker Integration

The application supports Freemarker templates for server-side rendering:

### Template Variables
- `employees`: List of employee objects
- `departments`: List of available departments
- `roles`: List of available roles
- `availableDepartments`: Department options for forms
- `availableRoles`: Role options for forms

### Employee Object Structure
```javascript
{
    id: number,
    firstName: string,
    lastName: string,
    email: string,
    department: string,
    role: string
}
```

### Example Controller Data
```java
// Java example for populating Freemarker template
model.addAttribute("employees", employeeService.getAllEmployees());
model.addAttribute("departments", employeeService.getDepartments());
model.addAttribute("roles", employeeService.getRoles());
```

## 🧪 Form Validation

### Client-side Validation Rules
- **First Name**: Required, 2-50 characters, letters/spaces/hyphens/apostrophes only
- **Last Name**: Required, 2-50 characters, letters/spaces/hyphens/apostrophes only
- **Email**: Required, valid email format, max 100 characters, unique
- **Department**: Required, must select from available options
- **Role**: Required, must select from available options

### Validation Features
- Real-time validation on field blur
- Error clearing on field input
- Form submission prevention if invalid
- Accessible error messages with ARIA labels

## 🎯 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📱 Mobile Features

- Touch-friendly interface
- Swipe gestures support
- Responsive typography
- Optimized button sizes
- Mobile-first design approach

## 🔒 Security Features

- Input sanitization
- XSS prevention
- HTML entity encoding
- Form validation
- CSRF protection ready (for server integration)

## 🚀 Performance Optimizations

- Debounced search input
- Efficient DOM manipulation
- CSS animations with GPU acceleration
- Lazy loading for large datasets
- Optimized event listeners

## 🛠️ Customization

### Styling
- Modify `css/styles.css` for visual customization
- Update `css/responsive.css` for responsive behavior
- CSS custom properties for easy theming

### Functionality
- Extend `js/data.js` for additional data operations
- Modify `js/validation.js` for custom validation rules
- Update `js/app.js` for new features

### Data Integration
- Replace mock data in `js/data.js` with API calls
- Implement server-side endpoints for CRUD operations
- Add authentication and authorization

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For questions or issues, please create an issue in the project repository or contact the development team.

---

**Built with ❤️ using vanilla JavaScript, modern CSS, and responsive design principles.**
