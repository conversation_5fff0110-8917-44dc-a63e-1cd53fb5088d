/**
 * Employee Directory Application
 * Main application logic and UI management
 */

// Application state
let currentEditingEmployee = null;

/**
 * Initialize the application when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize all application components
 */
function initializeApp() {
    setupEventListeners();
    renderEmployeeGrid();
    renderFilterOptions();
    renderPagination();
    
    // Set initial values for controls
    document.getElementById('sortSelect').value = currentState.sortBy;
    document.getElementById('showSelect').value = currentState.itemsPerPage;
}

/**
 * Setup all event listeners
 */
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    let searchTimeout;
    searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            updateSearch(e.target.value);
            renderEmployeeGrid();
            renderPagination();
        }, 300); // Debounce search
    });

    // Sort functionality
    document.getElementById('sortSelect').addEventListener('change', (e) => {
        updateSort(e.target.value);
        renderEmployeeGrid();
        renderPagination();
    });

    // Items per page functionality
    document.getElementById('showSelect').addEventListener('change', (e) => {
        updateItemsPerPage(e.target.value);
        renderEmployeeGrid();
        renderPagination();
    });

    // Filter sidebar
    document.getElementById('filterBtn').addEventListener('click', openFilterSidebar);
    document.getElementById('closeSidebar').addEventListener('click', closeFilterSidebar);
    document.getElementById('applyFilters').addEventListener('click', applyFilters);
    document.getElementById('clearFilters').addEventListener('click', clearFilters);

    // Add employee modal
    document.getElementById('addEmployeeBtn').addEventListener('click', openAddEmployeeModal);
    document.getElementById('closeModal').addEventListener('click', closeEmployeeModal);
    document.getElementById('cancelBtn').addEventListener('click', closeEmployeeModal);

    // Delete modal
    document.getElementById('closeDeleteModal').addEventListener('click', closeDeleteModal);
    document.getElementById('cancelDeleteBtn').addEventListener('click', closeDeleteModal);
    document.getElementById('confirmDeleteBtn').addEventListener('click', confirmDelete);

    // Employee form submission
    document.getElementById('employeeForm').addEventListener('submit', handleFormSubmission);

    // Overlay clicks
    document.getElementById('overlay').addEventListener('click', closeFilterSidebar);

    // Modal clicks (close on backdrop click)
    document.getElementById('employeeModal').addEventListener('click', (e) => {
        if (e.target.id === 'employeeModal') closeEmployeeModal();
    });
    document.getElementById('deleteModal').addEventListener('click', (e) => {
        if (e.target.id === 'deleteModal') closeDeleteModal();
    });

    // Keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);
}

/**
 * Handle keyboard navigation
 * @param {KeyboardEvent} e - Keyboard event
 */
function handleKeyboardNavigation(e) {
    // Close modals with Escape key
    if (e.key === 'Escape') {
        if (document.getElementById('employeeModal').classList.contains('active')) {
            closeEmployeeModal();
        } else if (document.getElementById('deleteModal').classList.contains('active')) {
            closeDeleteModal();
        } else if (document.getElementById('filterSidebar').classList.contains('active')) {
            closeFilterSidebar();
        }
    }
    
    // Quick search focus with Ctrl+F or Cmd+F
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        document.getElementById('searchInput').focus();
    }
    
    // Quick add employee with Ctrl+N or Cmd+N
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        openAddEmployeeModal();
    }
}

/**
 * Render the employee grid
 */
function renderEmployeeGrid() {
    const grid = document.getElementById('employeeGrid');
    const data = getPaginatedEmployees();
    
    if (data.employees.length === 0) {
        grid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h3>No employees found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = data.employees.map(employee => createEmployeeCard(employee)).join('');
    
    // Add event listeners to action buttons
    grid.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const employeeId = parseInt(e.target.closest('.employee-card').dataset.employeeId);
            openEditEmployeeModal(employeeId);
        });
    });
    
    grid.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const employeeId = parseInt(e.target.closest('.employee-card').dataset.employeeId);
            openDeleteModal(employeeId);
        });
    });
}

/**
 * Create HTML for employee card
 * @param {Object} employee - Employee object
 * @returns {string} HTML string for employee card
 */
function createEmployeeCard(employee) {
    const initials = `${employee.firstName.charAt(0)}${employee.lastName.charAt(0)}`.toUpperCase();
    
    return `
        <div class="employee-card" data-employee-id="${employee.id}">
            <div class="employee-header">
                <div class="employee-avatar">${initials}</div>
                <div class="employee-name">${employee.firstName} ${employee.lastName}</div>
            </div>
            <div class="employee-details">
                <div class="employee-detail">
                    <strong>Email:</strong>
                    <span>${employee.email}</span>
                </div>
                <div class="employee-detail">
                    <strong>Department:</strong>
                    <span>${employee.department}</span>
                </div>
                <div class="employee-detail">
                    <strong>Role:</strong>
                    <span>${employee.role}</span>
                </div>
            </div>
            <div class="employee-actions">
                <button class="edit-btn" title="Edit Employee">
                    <i class="fas fa-edit"></i>
                    Edit
                </button>
                <button class="delete-btn" title="Delete Employee">
                    <i class="fas fa-trash"></i>
                    Delete
                </button>
            </div>
        </div>
    `;
}

/**
 * Render pagination controls
 */
function renderPagination() {
    const pagination = document.getElementById('pagination');
    const data = getPaginatedEmployees();
    const { currentPage, totalPages, totalItems, startIndex, endIndex } = data.pagination;
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = `
        <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="goToPreviousPage()">
            <i class="fas fa-chevron-left"></i>
            Previous
        </button>
    `;
    
    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button class="pagination-btn" onclick="goToPageNumber(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="pagination-ellipsis">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="goToPageNumber(${i})">
                ${i}
            </button>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<span class="pagination-ellipsis">...</span>`;
        }
        paginationHTML += `<button class="pagination-btn" onclick="goToPageNumber(${totalPages})">${totalPages}</button>`;
    }
    
    paginationHTML += `
        <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="goToNextPage()">
            Next
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    paginationHTML += `
        <div class="pagination-info">
            Showing ${startIndex}-${endIndex} of ${totalItems} employees
        </div>
    `;
    
    pagination.innerHTML = paginationHTML;
}

/**
 * Pagination navigation functions
 */
function goToPreviousPage() {
    if (currentState.currentPage > 1) {
        goToPage(currentState.currentPage - 1);
        renderEmployeeGrid();
        renderPagination();
    }
}

function goToNextPage() {
    const totalPages = Math.ceil(currentState.filteredEmployees.length / currentState.itemsPerPage);
    if (currentState.currentPage < totalPages) {
        goToPage(currentState.currentPage + 1);
        renderEmployeeGrid();
        renderPagination();
    }
}

function goToPageNumber(page) {
    goToPage(page);
    renderEmployeeGrid();
    renderPagination();
}

/**
 * Render filter options in sidebar
 */
function renderFilterOptions() {
    // Render department filters
    const departmentFilters = document.getElementById('departmentFilters');
    const departments = getDepartments();
    
    departmentFilters.innerHTML = departments.map(dept => `
        <div class="filter-option">
            <input type="checkbox" id="dept-${dept}" value="${dept}" ${currentState.filters.departments.includes(dept) ? 'checked' : ''}>
            <label for="dept-${dept}">${dept}</label>
        </div>
    `).join('');
    
    // Render role filters
    const roleFilters = document.getElementById('roleFilters');
    const roles = getRoles();
    
    roleFilters.innerHTML = roles.map(role => `
        <div class="filter-option">
            <input type="checkbox" id="role-${role}" value="${role}" ${currentState.filters.roles.includes(role) ? 'checked' : ''}>
            <label for="role-${role}">${role}</label>
        </div>
    `).join('');
}

/**
 * Filter sidebar functions
 */
function openFilterSidebar() {
    document.getElementById('filterSidebar').classList.add('active');
    document.getElementById('overlay').classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeFilterSidebar() {
    document.getElementById('filterSidebar').classList.remove('active');
    document.getElementById('overlay').classList.remove('active');
    document.body.style.overflow = '';
}

function applyFilters() {
    const departments = Array.from(document.querySelectorAll('#departmentFilters input:checked')).map(cb => cb.value);
    const roles = Array.from(document.querySelectorAll('#roleFilters input:checked')).map(cb => cb.value);
    
    updateFilters({ departments, roles });
    renderEmployeeGrid();
    renderPagination();
    closeFilterSidebar();
    
    // Show filter applied message
    const filterCount = departments.length + roles.length;
    if (filterCount > 0) {
        showSuccessMessage(`${filterCount} filter(s) applied successfully`);
    }
}

function clearFilters() {
    // Uncheck all checkboxes
    document.querySelectorAll('#departmentFilters input, #roleFilters input').forEach(cb => {
        cb.checked = false;
    });
    
    // Clear search
    document.getElementById('searchInput').value = '';
    
    // Apply clear filters
    clearAllFilters();
    renderEmployeeGrid();
    renderPagination();
    closeFilterSidebar();
    
    showSuccessMessage('All filters cleared successfully');
}

/**
 * Modal functions
 */
function openAddEmployeeModal() {
    currentEditingEmployee = null;
    document.getElementById('modalTitle').textContent = 'Add Employee';
    document.getElementById('submitBtn').textContent = 'Add Employee';
    document.getElementById('employeeForm').reset();
    clearAllFieldErrors();
    
    document.getElementById('employeeModal').classList.add('active');
    document.body.style.overflow = 'hidden';
    
    // Focus first field
    setTimeout(() => {
        document.getElementById('firstName').focus();
    }, 100);
    
    // Setup real-time validation
    setupRealTimeValidation(document.getElementById('employeeForm'));
}

function openEditEmployeeModal(employeeId) {
    const employee = getEmployeeById(employeeId);
    if (!employee) return;
    
    currentEditingEmployee = employee;
    document.getElementById('modalTitle').textContent = 'Edit Employee';
    document.getElementById('submitBtn').textContent = 'Update Employee';
    
    // Populate form
    document.getElementById('firstName').value = employee.firstName;
    document.getElementById('lastName').value = employee.lastName;
    document.getElementById('email').value = employee.email;
    document.getElementById('department').value = employee.department;
    document.getElementById('role').value = employee.role;
    
    clearAllFieldErrors();
    
    document.getElementById('employeeModal').classList.add('active');
    document.body.style.overflow = 'hidden';
    
    // Focus first field
    setTimeout(() => {
        document.getElementById('firstName').focus();
    }, 100);
    
    // Setup real-time validation with exclude ID
    setupRealTimeValidation(document.getElementById('employeeForm'), employee.id);
}

function closeEmployeeModal() {
    document.getElementById('employeeModal').classList.remove('active');
    document.body.style.overflow = '';
    currentEditingEmployee = null;
    clearAllFieldErrors();
}

/**
 * Handle form submission
 * @param {Event} e - Form submit event
 */
function handleFormSubmission(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = getSanitizedFormData(form);
    
    // Validate form
    const validation = validateForm(formData, currentEditingEmployee?.id);
    
    if (!validation.isValid) {
        showFormErrors(validation.errors);
        // Focus first error field
        if (validation.firstErrorField) {
            document.getElementById(validation.firstErrorField).focus();
        }
        return;
    }
    
    try {
        if (currentEditingEmployee) {
            // Update existing employee
            updateEmployee(currentEditingEmployee.id, formData);
            showSuccessMessage(`${formData.firstName} ${formData.lastName} updated successfully`);
        } else {
            // Add new employee
            addEmployee(formData);
            showSuccessMessage(`${formData.firstName} ${formData.lastName} added successfully`);
        }
        
        renderEmployeeGrid();
        renderPagination();
        renderFilterOptions(); // Update filter options in case new departments/roles were added
        closeEmployeeModal();
        
    } catch (error) {
        showErrorMessage('An error occurred while saving the employee. Please try again.');
        console.error('Error saving employee:', error);
    }
}

/**
 * Delete modal functions
 */
let employeeToDelete = null;

function openDeleteModal(employeeId) {
    const employee = getEmployeeById(employeeId);
    if (!employee) return;
    
    employeeToDelete = employee;
    document.getElementById('deleteEmployeeName').textContent = `${employee.firstName} ${employee.lastName}`;
    document.getElementById('deleteModal').classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.remove('active');
    document.body.style.overflow = '';
    employeeToDelete = null;
}

function confirmDelete() {
    if (!employeeToDelete) return;
    
    try {
        const employeeName = `${employeeToDelete.firstName} ${employeeToDelete.lastName}`;
        deleteEmployee(employeeToDelete.id);
        
        renderEmployeeGrid();
        renderPagination();
        renderFilterOptions(); // Update filter options
        closeDeleteModal();
        
        showSuccessMessage(`${employeeName} deleted successfully`);
        
    } catch (error) {
        showErrorMessage('An error occurred while deleting the employee. Please try again.');
        console.error('Error deleting employee:', error);
    }
}
