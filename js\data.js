/**
 * Employee Data Management
 * This file handles all employee data operations including CRUD operations,
 * filtering, sorting, and pagination.
 */

// Mock employee data - simulates data that would come from Freemarker template
let employees = [
    {
        id: 1,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        department: 'HR',
        role: 'Manager'
    },
    {
        id: 2,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        department: 'IT',
        role: 'Developer'
    },
    {
        id: 3,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        department: 'Finance',
        role: 'Analyst'
    },
    {
        id: 4,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        department: 'Marketing',
        role: 'Designer'
    },
    {
        id: 5,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        department: 'Sales',
        role: 'Manager'
    },
    {
        id: 6,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        department: 'IT',
        role: 'Developer'
    },
    {
        id: 7,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        department: 'Operations',
        role: 'Coordinator'
    },
    {
        id: 8,
        firstName: '<PERSON>',
        last<PERSON>ame: 'Garcia',
        email: '<EMAIL>',
        department: 'HR',
        role: 'Specialist'
    },
    {
        id: 9,
        firstName: 'Ivan',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        department: 'Finance',
        role: 'Manager'
    },
    {
        id: 10,
        firstName: 'Julia',
        lastName: 'Martinez',
        email: '<EMAIL>',
        department: 'Marketing',
        role: 'Associate'
    },
    {
        id: 11,
        firstName: 'Kevin',
        lastName: 'Anderson',
        email: '<EMAIL>',
        department: 'IT',
        role: 'Manager'
    },
    {
        id: 12,
        firstName: 'Laura',
        lastName: 'Taylor',
        email: '<EMAIL>',
        department: 'Sales',
        role: 'Associate'
    },
    {
        id: 13,
        firstName: 'Michael',
        lastName: 'Thomas',
        email: '<EMAIL>',
        department: 'Operations',
        role: 'Specialist'
    },
    {
        id: 14,
        firstName: 'Nancy',
        lastName: 'Jackson',
        email: '<EMAIL>',
        department: 'HR',
        role: 'Director'
    },
    {
        id: 15,
        firstName: 'Oliver',
        lastName: 'White',
        email: '<EMAIL>',
        department: 'Finance',
        role: 'Analyst'
    }
];

// Current state management
let currentState = {
    filteredEmployees: [...employees],
    currentPage: 1,
    itemsPerPage: 10,
    sortBy: 'firstName',
    sortOrder: 'asc',
    searchTerm: '',
    filters: {
        departments: [],
        roles: []
    }
};

/**
 * Get all unique departments from employees
 * @returns {Array} Array of unique department names
 */
function getDepartments() {
    return [...new Set(employees.map(emp => emp.department))].sort();
}

/**
 * Get all unique roles from employees
 * @returns {Array} Array of unique role names
 */
function getRoles() {
    return [...new Set(employees.map(emp => emp.role))].sort();
}

/**
 * Generate unique ID for new employee
 * @returns {number} New unique ID
 */
function generateId() {
    return Math.max(...employees.map(emp => emp.id), 0) + 1;
}

/**
 * Add new employee
 * @param {Object} employeeData - Employee data object
 * @returns {Object} Added employee with ID
 */
function addEmployee(employeeData) {
    const newEmployee = {
        id: generateId(),
        ...employeeData
    };
    
    employees.push(newEmployee);
    applyFiltersAndSort();
    return newEmployee;
}

/**
 * Update existing employee
 * @param {number} id - Employee ID
 * @param {Object} employeeData - Updated employee data
 * @returns {Object|null} Updated employee or null if not found
 */
function updateEmployee(id, employeeData) {
    const index = employees.findIndex(emp => emp.id === id);
    if (index === -1) return null;
    
    employees[index] = { ...employees[index], ...employeeData };
    applyFiltersAndSort();
    return employees[index];
}

/**
 * Delete employee by ID
 * @param {number} id - Employee ID
 * @returns {boolean} True if deleted, false if not found
 */
function deleteEmployee(id) {
    const index = employees.findIndex(emp => emp.id === id);
    if (index === -1) return false;
    
    employees.splice(index, 1);
    applyFiltersAndSort();
    return true;
}

/**
 * Get employee by ID
 * @param {number} id - Employee ID
 * @returns {Object|null} Employee object or null if not found
 */
function getEmployeeById(id) {
    return employees.find(emp => emp.id === id) || null;
}

/**
 * Apply search filter to employees
 * @param {Array} employeeList - List of employees to filter
 * @param {string} searchTerm - Search term
 * @returns {Array} Filtered employees
 */
function applySearch(employeeList, searchTerm) {
    if (!searchTerm.trim()) return employeeList;
    
    const term = searchTerm.toLowerCase().trim();
    return employeeList.filter(emp => 
        emp.firstName.toLowerCase().includes(term) ||
        emp.lastName.toLowerCase().includes(term) ||
        emp.email.toLowerCase().includes(term) ||
        `${emp.firstName} ${emp.lastName}`.toLowerCase().includes(term)
    );
}

/**
 * Apply department and role filters
 * @param {Array} employeeList - List of employees to filter
 * @param {Object} filters - Filter object with departments and roles arrays
 * @returns {Array} Filtered employees
 */
function applyFilters(employeeList, filters) {
    let filtered = employeeList;
    
    if (filters.departments.length > 0) {
        filtered = filtered.filter(emp => filters.departments.includes(emp.department));
    }
    
    if (filters.roles.length > 0) {
        filtered = filtered.filter(emp => filters.roles.includes(emp.role));
    }
    
    return filtered;
}

/**
 * Sort employees by specified field and order
 * @param {Array} employeeList - List of employees to sort
 * @param {string} sortBy - Field to sort by
 * @param {string} sortOrder - 'asc' or 'desc'
 * @returns {Array} Sorted employees
 */
function sortEmployees(employeeList, sortBy, sortOrder) {
    return [...employeeList].sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];
        
        // Handle string comparison
        if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
            bValue = bValue.toLowerCase();
        }
        
        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
        return 0;
    });
}

/**
 * Apply all filters, search, and sorting to employee list
 */
function applyFiltersAndSort() {
    let filtered = [...employees];
    
    // Apply search
    filtered = applySearch(filtered, currentState.searchTerm);
    
    // Apply filters
    filtered = applyFilters(filtered, currentState.filters);
    
    // Apply sorting
    filtered = sortEmployees(filtered, currentState.sortBy, currentState.sortOrder);
    
    currentState.filteredEmployees = filtered;
    
    // Reset to first page if current page is beyond available pages
    const totalPages = Math.ceil(filtered.length / currentState.itemsPerPage);
    if (currentState.currentPage > totalPages && totalPages > 0) {
        currentState.currentPage = 1;
    }
}

/**
 * Get paginated employees for current page
 * @returns {Object} Object with employees array and pagination info
 */
function getPaginatedEmployees() {
    const startIndex = (currentState.currentPage - 1) * currentState.itemsPerPage;
    const endIndex = startIndex + currentState.itemsPerPage;
    const paginatedEmployees = currentState.filteredEmployees.slice(startIndex, endIndex);
    
    return {
        employees: paginatedEmployees,
        pagination: {
            currentPage: currentState.currentPage,
            totalPages: Math.ceil(currentState.filteredEmployees.length / currentState.itemsPerPage),
            totalItems: currentState.filteredEmployees.length,
            itemsPerPage: currentState.itemsPerPage,
            startIndex: startIndex + 1,
            endIndex: Math.min(endIndex, currentState.filteredEmployees.length)
        }
    };
}

/**
 * Update search term and apply filters
 * @param {string} searchTerm - New search term
 */
function updateSearch(searchTerm) {
    currentState.searchTerm = searchTerm;
    currentState.currentPage = 1;
    applyFiltersAndSort();
}

/**
 * Update filters and apply them
 * @param {Object} filters - New filters object
 */
function updateFilters(filters) {
    currentState.filters = { ...filters };
    currentState.currentPage = 1;
    applyFiltersAndSort();
}

/**
 * Update sorting and apply it
 * @param {string} sortBy - Field to sort by
 * @param {string} sortOrder - Sort order ('asc' or 'desc')
 */
function updateSort(sortBy, sortOrder = 'asc') {
    currentState.sortBy = sortBy;
    currentState.sortOrder = sortOrder;
    applyFiltersAndSort();
}

/**
 * Update items per page and reset to first page
 * @param {number} itemsPerPage - Number of items per page
 */
function updateItemsPerPage(itemsPerPage) {
    currentState.itemsPerPage = parseInt(itemsPerPage);
    currentState.currentPage = 1;
}

/**
 * Go to specific page
 * @param {number} page - Page number
 */
function goToPage(page) {
    const totalPages = Math.ceil(currentState.filteredEmployees.length / currentState.itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentState.currentPage = page;
    }
}

/**
 * Clear all filters and search
 */
function clearAllFilters() {
    currentState.searchTerm = '';
    currentState.filters = { departments: [], roles: [] };
    currentState.currentPage = 1;
    applyFiltersAndSort();
}

// Initialize the filtered employees list
applyFiltersAndSort();
